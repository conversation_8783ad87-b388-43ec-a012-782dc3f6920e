/* Base Styles */
:root {
  --primary-color: #1e4c4a;
  --primary-dark: #17442e;
  --secondary-color: #6c757d;
  --dark-color: #1e293b;
  --light-color: #f8f9fa;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --body-bg: #ffffff;
  --body-color: #333;
  --border-color: #e9ecef;
  --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
  --container-width: 1200px;
  --branddark: #0b361f;
  --brandlight: #2f855a;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: var(--body-color);
  background-color: var(--body-bg);
  overflow-x: hidden;
}

a {
  text-decoration: none;
  color: var(--primary-color);
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

ul {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
}

.container {
  width: 100%;
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 20px;
}

.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  line-height: 1.3;
  margin-bottom: 1rem;
  color: var(--dark-color);
}

h1 {
  font-size: 3rem;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 2rem;
}

h4 {
  font-size: 1.5rem;
}

h5 {
  font-size: 1.25rem;
}

h6 {
  font-size: 1rem;
}

p {
  margin-bottom: 1.5rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--secondary-color);
  max-width: 700px;
  margin: 0 auto;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 30px;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: var(--transition);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  color: white;
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Header */
.header {
  padding: 20px 0;
  position: sticky;
  top: 0;
  background-color: white;
  box-shadow: var(--shadow);
  z-index: 1000;
}

.header-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.8rem;
  font-weight: 700;
}

.logo a {
  color: var(--dark-color);
}

.logo-text {
  color: var(--dark-color);
}

.logo-text span {
  color: var(--primary-color);
}

.main-nav {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  gap: 30px;
}

.nav-link {
  color: var(--dark-color);
  font-weight: 500;
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition);
}

.nav-link:hover::after {
  width: 100%;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
}

.mobile-menu-toggle span {
  display: block;
  width: 25px;
  height: 3px;
  background-color: var(--dark-color);
  margin: 5px 0;
  transition: var(--transition);
}

/* Hero Section */
.hero {
  padding: 100px 0;
  background-color: #f9fafb;
}

.hero .container {
  display: flex;
  align-items: center;
  gap: 50px;
}

.hero-content {
  flex: 1;
}

.hero-title {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.hero-text {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: var(--secondary-color);
}

.hero-buttons {
  display: flex;
  gap: 15px;
}

.hero-image {
  flex: 1;
  text-align: center;
}

/* Services Section */
.services-preview {
  padding: 100px 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.service-card {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: var(--shadow);
  transition: var(--transition);
  text-align: center;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.service-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.service-title {
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.service-description {
  color: var(--secondary-color);
  margin-bottom: 20px;
}

.service-link {
  font-weight: 600;
  display: inline-block;
  position: relative;
}

.service-link::after {
  content: "→";
  margin-left: 5px;
  transition: var(--transition);
}

.service-link:hover::after {
  margin-left: 10px;
}

.services-cta {
  text-align: center;
  margin-top: 50px;
}

/* About Section */
.about-preview {
  padding: 100px 0;
  background-color: #f9fafb;
}

.about-grid {
  display: flex;
  align-items: center;
  gap: 50px;
}

.about-image {
  flex: 1;
}

.about-content {
  flex: 1;
}

.about-text {
  color: var(--secondary-color);
  margin-bottom: 1.5rem;
}

/* Portfolio Section */
.portfolio-preview {
  padding: 100px 0;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.portfolio-item {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.portfolio-image {
  position: relative;
  overflow: hidden;
}

.portfolio-image img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: var(--transition);
}

.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: var(--transition);
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-item:hover .portfolio-image img {
  transform: scale(1.1);
}

.portfolio-link {
  background-color: white;
  color: var(--primary-color);
  padding: 10px 20px;
  border-radius: 5px;
  font-weight: 600;
}

.portfolio-info {
  padding: 20px;
  background-color: white;
}

.portfolio-title {
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.portfolio-category {
  color: var(--secondary-color);
  font-size: 0.9rem;
}

.portfolio-cta {
  text-align: center;
  margin-top: 50px;
}

/* Testimonials Section */
.testimonials {
  padding: 100px 0;
  background-color: #f9fafb;
}

.testimonials-slider {
  display: flex;
  gap: 30px;
  margin-top: 50px;
  overflow-x: auto;
  padding-bottom: 30px;
  scrollbar-width: none;
}

.testimonials-slider::-webkit-scrollbar {
  display: none;
}

.testimonial {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: var(--shadow);
  min-width: 350px;
  flex: 1;
}

.testimonial-content {
  margin-bottom: 20px;
}

.testimonial-text {
  font-style: italic;
  color: var(--secondary-color);
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 15px;
}

.testimonial-author-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
}

.testimonial-author-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.testimonial-author-name {
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.testimonial-author-title {
  color: var(--secondary-color);
  font-size: 0.9rem;
}

/* CTA Section */
.cta {
  padding: 80px 0;
  background-color: var(--primary-color);
  color: white;
  text-align: center;
}

.cta-title {
  color: white;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.cta-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2rem;
  margin-bottom: 2rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.cta .btn-primary {
  background-color: white;
  color: var(--primary-color);
}

.cta .btn-primary:hover {
  background-color: rgba(255, 255, 255, 0.9);
}

/* Footer */
.footer {
  background-color: var(--dark-color);
  color: white;
  padding: 80px 0 20px;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-bottom: 50px;
}

.footer-title {
  color: white;
  font-size: 1.5rem;
  margin-bottom: 20px;
  position: relative;
}

.footer-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.footer-text {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-link {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  transition: var(--transition);
}

.social-link:hover {
  background-color: var(--primary-color);
  color: white;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  transition: var(--transition);
}

.footer-links a:hover {
  color: white;
  padding-left: 5px;
}

.contact-info li {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  color: rgba(255, 255, 255, 0.7);
}

.contact-info i {
  color: var(--primary-color);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 20px;
  text-align: center;
}

.copyright {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* Page Header */
.page-header {
  background-color: var(--primary-color);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.page-title {
  color: white;
  font-size: 3rem;
  margin-bottom: 1rem;
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto;
}

/* About Page */
.about-section {
  padding: 100px 0;
}

.values-section {
  padding: 100px 0;
  background-color: #f9fafb;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.value-card {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: var(--shadow);
  text-align: center;
  transition: var(--transition);
}

.value-card:hover {
  transform: translateY(-10px);
}

.value-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.value-title {
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.value-description {
  color: var(--secondary-color);
}

.team-section {
  padding: 100px 0;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.team-member {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.team-member-image {
  height: 300px;
  overflow: hidden;
}

.team-member-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.team-member:hover .team-member-image img {
  transform: scale(1.1);
}

.team-member-info {
  padding: 20px;
}

.team-member-name {
  font-size: 1.5rem;
  margin-bottom: 5px;
}

.team-member-role {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 15px;
}

.team-member-bio {
  color: var(--secondary-color);
  margin-bottom: 15px;
}

.team-member-social {
  display: flex;
  gap: 10px;
}

.stats-section {
  padding: 80px 0;
  background-color: var(--primary-color);
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
}

/* Services Page */
.service-section {
  padding: 100px 0;
}

.service-section.alt-bg {
  background-color: #f9fafb;
}

.service-grid {
  display: flex;
  align-items: center;
  gap: 50px;
}

.service-grid.reverse {
  flex-direction: row-reverse;
}

.service-content {
  flex: 1;
}

.service-image {
  flex: 1;
  text-align: center;
}

.service-features {
  margin-bottom: 30px;
}

.service-features li {
  margin-bottom: 10px;
  position: relative;
  padding-left: 30px;
}

.service-features li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: 700;
}

.process-section {
  padding: 100px 0;
  background-color: #f9fafb;
}

.process-steps {
  max-width: 800px;
  margin: 50px auto 0;
}

.process-step {
  display: flex;
  gap: 30px;
  margin-bottom: 50px;
  position: relative;
}

.process-step:last-child {
  margin-bottom: 0;
}

.process-step::after {
  content: "";
  position: absolute;
  top: 70px;
  left: 35px;
  width: 2px;
  height: calc(100% - 20px);
  background-color: var(--primary-color);
}

.process-step:last-child::after {
  display: none;
}

.process-number {
  width: 70px;
  height: 70px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  flex-shrink: 0;
  z-index: 1;
}

.process-content {
  flex: 1;
}

.process-title {
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.process-description {
  color: var(--secondary-color);
}

/* Portfolio Page */
.portfolio-filters {
  padding: 30px 0;
  background-color: #f9fafb;
}

.filters-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
}

.filter-btn {
  padding: 10px 20px;
  background-color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
}

.filter-btn.active,
.filter-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

.portfolio-gallery {
  padding: 80px 0;
}

/* Contact Page */
.contact-section {
  padding: 100px 0;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 50px;
}

.contact-text {
  color: var(--secondary-color);
  margin-bottom: 30px;
}

.contact-details {
  margin-bottom: 30px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
}

.contact-icon {
  width: 50px;
  height: 50px;
  background-color: rgba(74, 108, 247, 0.1);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--primary-color);
  font-size: 1.2rem;
  flex-shrink: 0;
}

.contact-item-title {
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.contact-item-text {
  color: var(--secondary-color);
}

.contact-form-container {
  background-color: white;
  border-radius: 10px;
  padding: 40px;
  box-shadow: var(--shadow);
}

.contact-form .form-group {
  margin-bottom: 20px;
}

.contact-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.contact-form input,
.contact-form textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  font-family: inherit;
  font-size: 1rem;
  transition: var(--transition);
}

.contact-form input:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.2);
}

.form-status {
  margin-top: 20px;
  padding: 10px;
  border-radius: 5px;
  display: none;
}

.form-status.success {
  display: block;
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.form-status.error {
  display: block;
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-color);
  border: 1px solid var(--danger-color);
}

.map-section {
  padding-bottom: 100px;
}

.map-container {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--shadow);
}

/* Error Pages */
.error-section {
  padding: 100px 0;
  display: flex;
  align-items: center;
  min-height: 70vh;
}

.error-section .container {
  display: flex;
  align-items: center;
  gap: 50px;
}

.error-content {
  flex: 1;
}

.error-title {
  font-size: 8rem;
  color: var(--primary-color);
  line-height: 1;
  margin-bottom: 0;
}

.error-subtitle {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.error-text {
  color: var(--secondary-color);
  margin-bottom: 30px;
}

.error-image {
  flex: 1;
  text-align: center;
}

.error-details {
  background-color: rgba(220, 53, 69, 0.1);
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 30px;
  color: var(--danger-color);
}

/* Responsive Styles */
@media (max-width: 992px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .hero .container,
  .about-grid,
  .service-grid,
  .error-section .container {
    flex-direction: column;
    text-align: center;
  }

  .service-grid.reverse {
    flex-direction: column;
  }

  .about-image,
  .service-image {
    order: -1;
  }

  .service-features li {
    text-align: left;
  }

  .footer-title::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .contact-grid {
    grid-template-columns: 1fr;
  }

  .contact-info {
    text-align: center;
  }

  .contact-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .nav-list {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: white;
    flex-direction: column;
    padding: 20px;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    gap: 15px;
  }

  .nav-list.active {
    display: flex;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .testimonials-slider {
    flex-direction: column;
  }

  .testimonial {
    min-width: auto;
  }

  .portfolio-grid,
  .services-grid,
  .values-grid,
  .team-grid,
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .process-step {
    flex-direction: column;
    text-align: center;
  }

  .process-number {
    margin: 0 auto;
  }

  .process-step::after {
    left: 50%;
    top: 70px;
    height: calc(100% - 70px);
    transform: translateX(-50%);
  }
}

@media (max-width: 576px) {
  .container {
    padding: 0 15px;
  }

  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.8rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .error-title {
    font-size: 5rem;
  }

  .error-subtitle {
    font-size: 1.8rem;
  }

  .footer-grid {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }

  .contact-form-container {
    padding: 20px;
  }
}
