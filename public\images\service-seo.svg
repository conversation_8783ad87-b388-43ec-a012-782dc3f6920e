<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 600 400">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
      }

      .cls-2 {
        fill: #f3f4f6;
      }

      .cls-3 {
        fill: none;
        stroke: #17442e;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 5px;
      }

      .cls-4 {
        fill: #e5e7eb;
      }

      .cls-5 {
        fill: #17442e;
      }
    </style>
  </defs>
  <rect class="cls-2" width="600" height="400"/>
  <rect class="cls-1" x="150" y="100" width="300" height="200" rx="10" ry="10"/>
  <path class="cls-3" d="M200,200l50-50,50,100,50-70,50,40"/>
  <circle class="cls-5" cx="200" cy="200" r="8"/>
  <circle class="cls-5" cx="250" cy="150" r="8"/>
  <circle class="cls-5" cx="300" cy="250" r="8"/>
  <circle class="cls-5" cx="350" cy="180" r="8"/>
  <circle class="cls-5" cx="400" cy="220" r="8"/>
  <rect class="cls-4" x="180" y="280" width="240" height="10" rx="2" ry="2"/>
  <rect class="cls-4" x="180" y="300" width="180" height="10" rx="2" ry="2"/>
</svg>