const Contact = require('../models/Contact');

// @desc    Submit contact form
// @route   POST /api/contact
// @access  Public
exports.submitContact = async (req, res) => {
  try {
    const { name, email, phone, message } = req.body;
    
    // Create contact
    const contact = await Contact.create({
      name,
      email,
      phone,
      message
    });
    
    // Here you would typically integrate with EmailJS to send an email
    // For now, we'll just log the contact information
    console.log('New contact submission:', contact);
    
    res.status(201).json({
      success: true,
      data: contact
    });
  } catch (error) {
    console.error(error);
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        error: messages
      });
    } else {
      return res.status(500).json({
        success: false,
        error: 'Server Error'
      });
    }
  }
};

// @desc    Get contact form page
// @route   GET /contact
// @access  Public
exports.getContactPage = (req, res) => {
  res.render('contact', {
    title: 'Contact Us',
    description: 'Get in touch with our web development agency'
  });
};
