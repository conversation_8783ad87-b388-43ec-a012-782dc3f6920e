{"name": "weblabs", "version": "1.0.0", "description": "Modern Web Development Agency Website", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1", "deploy": "node deploy.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"archiver": "^5.3.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^5.1.0", "mongoose": "^8.13.2", "weblabs": "file:"}, "devDependencies": {"nodemon": "^3.1.9"}}