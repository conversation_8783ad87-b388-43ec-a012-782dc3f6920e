<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 600 400">
  <defs>
    <style>
      .cls-1 {
        fill: #d1d5db;
      }

      .cls-2 {
        fill: #fff;
      }

      .cls-3 {
        fill: #f3f4f6;
      }

      .cls-4 {
        fill: #e5e7eb;
      }

      .cls-5 {
        fill: #17442e;
      }
    </style>
  </defs>
  <rect class="cls-3" width="600" height="400"/>
  <rect class="cls-4" x="100" y="50" width="400" height="300" rx="10" ry="10"/>
  <rect class="cls-1" x="150" y="100" width="300" height="200" rx="5" ry="5"/>
  <rect class="cls-5" x="200" y="150" width="200" height="100" rx="3" ry="3"/>
  <circle class="cls-2" cx="250" cy="200" r="30"/>
  <rect class="cls-2" x="300" y="180" width="80" height="10" rx="2" ry="2"/>
  <rect class="cls-2" x="300" y="200" width="60" height="10" rx="2" ry="2"/>
  <rect class="cls-2" x="300" y="220" width="40" height="10" rx="2" ry="2"/>
</svg>