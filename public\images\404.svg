<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 600 400">
  <defs>
    <style>
      .cls-1 {
        fill: #f3f4f6;
      }

      .cls-2 {
        fill: none;
        stroke: #17442e;
        stroke-linecap: round;
        stroke-width: 5px;
      }

      .cls-3 {
        fill: #17442e;
      }
    </style>
  </defs>
  <rect class="cls-1" width="600" height="400"/>
  <path class="cls-3" d="M150,150h50v100h-50v-100Z"/>
  <path class="cls-3" d="M220,150h50v100h-50v-100Z"/>
  <path class="cls-3" d="M290,150h50v100h-50v-100Z"/>
  <circle class="cls-3" cx="400" cy="200" r="50"/>
  <path class="cls-2" d="M150,300h250"/>
  <path class="cls-2" d="M200,100h150"/>
</svg>