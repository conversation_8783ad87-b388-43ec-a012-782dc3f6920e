<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 600 400">
  <defs>
    <style>
      .cls-1 {
        fill: #f3f4f6;
      }

      .cls-2, .cls-3 {
        stroke-width: 10px;
      }

      .cls-2, .cls-3, .cls-4 {
        fill: none;
        stroke: #17442e;
        stroke-linecap: round;
      }

      .cls-3 {
        stroke-linejoin: round;
      }

      .cls-4 {
        stroke-width: 5px;
      }

      .cls-5 {
        fill: #17442e;
      }
    </style>
  </defs>
  <rect class="cls-1" width="600" height="400"/>
  <path class="cls-5" d="M150,150s0-50,50-50h50c50,0,50,50,50,50v25s0,25-50,25h-50c-50,0-50-25-50-25v-25Z"/>
  <path class="cls-2" d="M150,250v-50"/>
  <path class="cls-3" d="M350,150s0-50,50-50h50c50,0,50,50,50,50v100"/>
  <circle class="cls-5" cx="350" cy="200" r="50"/>
  <path class="cls-4" d="M150,300h350"/>
</svg>