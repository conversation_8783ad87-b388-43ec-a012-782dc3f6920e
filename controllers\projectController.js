const Project = require('../models/Project');

// @desc    Get all projects
// @route   GET /api/projects
// @access  Public
exports.getProjects = async (req, res) => {
  try {
    const projects = await Project.find().sort({ createdAt: -1 });
    
    return res.status(200).json({
      success: true,
      count: projects.length,
      data: projects
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// @desc    Get featured projects
// @route   GET /api/projects/featured
// @access  Public
exports.getFeaturedProjects = async (req, res) => {
  try {
    const projects = await Project.find({ featured: true }).sort({ createdAt: -1 });
    
    return res.status(200).json({
      success: true,
      count: projects.length,
      data: projects
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// @desc    Get portfolio page
// @route   GET /portfolio
// @access  Public
exports.getPortfolioPage = async (req, res) => {
  try {
    const projects = await Project.find().sort({ createdAt: -1 });
    
    res.render('portfolio', {
      title: 'Our Portfolio',
      description: 'Check out our latest web development projects',
      projects
    });
  } catch (error) {
    console.error(error);
    res.status(500).render('error', {
      title: 'Server Error',
      error
    });
  }
};
