const express = require('express');
const router = express.Router();
const { getContactPage } = require('../controllers/contactController');
const { getPortfolioPage } = require('../controllers/projectController');

// Home page
router.get('/', (req, res) => {
  res.render('index', {
    title: 'WebLabs - Modern Web Development Agency',
    description: 'We create stunning websites and web applications for businesses'
  });
});

// About page
router.get('/about', (req, res) => {
  res.render('about', {
    title: 'About Us',
    description: 'Learn more about our web development agency'
  });
});

// Services page
router.get('/services', (req, res) => {
  res.render('services', {
    title: 'Our Services',
    description: 'Explore our web development services'
  });
});

// Portfolio page
router.get('/portfolio', getPortfolioPage);

// Contact page
router.get('/contact', getContactPage);

// 404 page
router.get('/404', (req, res) => {
  res.render('404', {
    title: 'Page Not Found',
    description: 'The page you are looking for does not exist'
  });
});

// Error page
router.get('/error', (req, res) => {
  res.render('error', {
    title: 'Server Error',
    description: 'Something went wrong',
    error: { message: 'Server error' }
  });
});

module.exports = router;
